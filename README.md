# RORSCHOOLS

## Ruby version
- ruby-3.4.5

## System dependencies
- ### [Active Storage](https://guides.rubyonrails.org/active_storage_overview.html#requirements)
    - in ubuntu install libvips
    - `sudo apt install -y libvips42 libvips-dev`
    - or use ImageMagick
    - `config.active_storage.variant_processor :mini_magick`
- ### [TipTap](https://tiptap.dev/docs/editor/getting-started/install/vanilla-javascript)
    - `pnpm install @tiptap/core @tiptap/starter-kit @tiptap/extension-image @tiptap/extension-text-align @tiptap/extension-link @tiptap/extension-placeholder`
## Configuration

## Database creation

## Database initialization

## How to run the test suite

## Services (job queues, cache servers, search engines, etc.)

## Deployment instructions

## ...

## config/sitemap.rb
```
SitemapGenerator::Sitemap.create do
  Post.published.find_each do |post|
    add post_path(post), 
        lastmod: post.updated_at,
        changefreq: 'weekly',
        priority: 0.8,
        images: post.featured_image.attached? ? [{ loc: url_for(post.featured_image), title: post.title }] : []
  end
end
```
```
bin/rails sitemap:install        # creates config/sitemap.rb task hooks if needed
bin/rails sitemap:refresh
```