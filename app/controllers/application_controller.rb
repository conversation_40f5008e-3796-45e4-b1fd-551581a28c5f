class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

  # Switch locale based on params[:locale] https://guides.rubyonrails.org/i18n.html#managing-the-locale-across-requests
  around_action :switch_locale

  # Use different layout for devise controllers
  layout :layout_by_resource

  def switch_locale(&action)
    locale = params[:locale] || detect_locale || I18n.default_locale
    I18n.with_locale(locale, &action)
  end

  # Setting the Locale from URL Params
  def default_url_options
    { locale: I18n.locale }
  end

  # Detect locale from browser
  def detect_locale
     preferred_languages = request.env["HTTP_ACCEPT_LANGUAGE"]&.scan(/\w{2}/)&.map(&:downcase)
     available = I18n.available_locales.map(&:to_s)
     detected_locale = preferred_languages&.find { |lang| available.include?(lang) }
     detected_locale&.to_sym
  end

  def layout_by_resource
    if devise_controller?
      "auth"
    else
      "application"
    end
  end

  def after_sign_in_path_for(resource)
    if resource.viewer?
      root_path(locale: I18n.locale)
    else
      dashboard_root_path(locale: I18n.locale)
    end
  end

  def after_sign_up_path_for(resource)
    if resource.viewer?
      root_path(locale: I18n.locale)
    else
      dashboard_root_path(locale: I18n.locale)
    end
  end

  rescue_from CanCan::AccessDenied do |exception|
    respond_to do |format|
      format.json { render nothing: true, status: :forbidden }
      format.html { redirect_to access_denied_path(locale: I18n.locale, message: exception.message), status: :forbidden }
      format.js   { render nothing: true, status: :forbidden }
    end
  end
end
