class ErrorsController < ApplicationController
  layout "application"
  def access_denied
    @message = params[:message] || "You are not authorized to access this page."
    respond_to do |format|
      format.html { render :access_denied, status: :forbidden }
      format.json { render json: { error: @message }, status: :forbidden }
      format.js   { render nothing: true, status: :forbidden }
    end
  end
end
