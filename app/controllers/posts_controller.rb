class PostsController < ApplicationController
  before_action :set_post, only: %i[ show edit update destroy ]
  layout "dashboard", except: [ :index, :show ]

  # GET /posts or /posts.json
  def index
    @posts = Post.published.order(published_at: :desc)
  end

  # GET /posts/1 or /posts/1.json
  def show
    if request.path != post_path(@post)
      redirect_to @post, status: :moved_permanently
    end

    image_url = url_for(@post.featured_image) if @post.featured_image.attached?

    set_meta_tags(
      title: @post.effective_seo_title,
      description: @post.effective_meta_description,
      canonical: post_url(@post),
      og: {
        type: "article",
        image: image_url
      }.compact,
      twitter: {
        card: "summary_large_image",
        image: image_url
      }.compact
    )
  end

  # GET /posts/new
  def new
    @post = Post.new
  end

  # GET /posts/1/edit
  def edit
  end

  # POST /posts or /posts.json
  def create
    @post = Post.new(post_params)

    respond_to do |format|
      if @post.save
        format.html { redirect_to @post, notice: "Post was successfully created." }
        format.json { render :show, status: :created, location: @post }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @post.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /posts/1 or /posts/1.json
  def update
    respond_to do |format|
      if @post.update(post_params)
        format.html { redirect_to @post, notice: "Post was successfully updated.", status: :see_other }
        format.json { render :show, status: :ok, location: @post }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @post.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /posts/1 or /posts/1.json
  def destroy
    @post.destroy!

    respond_to do |format|
      format.html { redirect_to posts_path, notice: "Post was successfully destroyed.", status: :see_other }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_post
      @post = Post.friendly.find(params[:slug])
    end

    # Only allow a list of trusted parameters through.
    def post_params
      params.require(:post).permit(:title, :slug, :excerpt, :body, :seo_title, :meta_description, :published, :published_at, :featured_image)
    end
end
