import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = {
    light: String,
    dark: String
  }

  connect() {    
    this.updateCheckboxState()
  }

  toggle() {
    const currentTheme = document.documentElement.getAttribute('data-theme')
    const newTheme = currentTheme === this.darkValue ? this.lightValue : this.darkValue
    document.documentElement.setAttribute('data-theme', newTheme)
    localStorage.setItem('theme', newTheme)
  }

  updateCheckboxState() {
    const checkbox = this.element.querySelector("input[type=checkbox]")
    if (checkbox) {
      const currentTheme = document.documentElement.getAttribute('data-theme')
      checkbox.checked = (currentTheme === this.darkValue)
    }
  }
}