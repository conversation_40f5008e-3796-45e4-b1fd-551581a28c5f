class Post < ApplicationRecord
  extend FriendlyId
  friendly_id :title, use: [ :slugged, :history ]

  has_one_attached :featured_image

  validates :title, presence: true, length: { maximum: 120 }
  validates :excerpt, presence: true, length: { maximum: 160 }
  validates :body, presence: true

  scope :published, -> { where(published: true).where("published_at <= ?", Time.current) }

  def should_generate_new_friendly_id?
    will_save_change_to_title? || super
  end

  def effective_seo_title
    seo_title.presence || title
  end

  def effective_meta_description
    meta_description.presence || excerpt&.truncate(160)
  end
end
