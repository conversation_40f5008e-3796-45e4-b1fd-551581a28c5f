class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable
  validates :role, inclusion: { in: %w[admin editor viewer], message: "%{value} is not a valid role" }
  after_initialize :set_default_role, if: :new_record?

  def set_default_role
    self.role ||= "viewer"
  end

  def admin?
    role == "admin"
  end

  def editor?
    role == "editor"
  end

  def viewer?
    role == "viewer"
  end
end
