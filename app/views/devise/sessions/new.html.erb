  <!-- Split Screen Login Form -->
<div class="min-h-screen flex">
  <!-- Image Side (Hidden on Small Screens) -->
  <div class="hidden lg:block lg:w-1/2 bg-cover bg-center" style="background-image: url('https://picsum.photos/800/1000');" loading="lazy">
    <!-- Optional: Add overlay with opacity e.g. bg-black bg-opacity-50 -->
    <!-- Optional: Add text content centered within this div -->
  </div>

  <!-- Form Side -->
  <div class="w-full lg:w-1/2 flex items-center justify-center p-8 sm:p-12 bg-base-100">
    <div class="w-full max-w-md">
       <!-- Optional: Logo can go here -->
       <div class="text-left mb-8">
         <span class="text-3xl font-bold text-primary">Login</span>
         <p class="text-secondary mb-8">Welcome back! Please enter your details.</p>
       </div>      

      <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
        <div class="mt-4">
          <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-1" %>
          <%= f.email_field :email, autofocus: true, autocomplete: "email" , class: "input w-full input-primary"%>          
        </div>
        <div class="mt-4">          
            <%= f.label :password, class: "block text-sm font-medium text-gray-700" %>
            <%= f.password_field :password, autocomplete: "current-password", class: "input w-full input-primary" %>            
        </div>
        <% if devise_mapping.rememberable? %>
          <div class="mt-4">
            <%= f.check_box :remember_me , class: "checkbox checkbox-primary" %>
            <%= f.label :remember_me %>
          </div>
        <% end %>
        <div class="mt-4">
          <%= f.submit "Log in" , class: "btn btn-primary w-full" %>
        </div>
      <% end %>

      <p class="text-center text-sm text-gray-500 mt-8">
        <%= render "devise/shared/links" %>        
      </p>
    </div>
  </div>
</div>