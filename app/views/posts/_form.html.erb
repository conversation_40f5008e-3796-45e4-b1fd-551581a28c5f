<%= form_with(model: post, class: "contents") do |form| %>
  <% if post.errors.any? %>
    <div id="error_explanation" class="bg-red-50 text-red-500 px-3 py-2 font-medium rounded-md mt-3">
      <h2><%= pluralize(post.errors.count, "error") %> prohibited this post from being saved:</h2>

      <ul class="list-disc ml-6">
        <% post.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>
  <div class="field">
    <%= form.label :featured_image %><br>
    <%= form.file_field :featured_image %>
  </div>
  <div class="my-5">
    <%= form.label :title %>
    <%= form.text_field :title, class: ["block shadow-sm rounded-md border px-3 py-2 mt-2 w-full", {"border-gray-400 focus:outline-blue-600": post.errors[:title].none?, "border-red-400 focus:outline-red-600": post.errors[:title].any?}] %>
  </div>

  <div class="my-5">
    <%= form.label :slug %>
    <%= form.text_field :slug, class: ["block shadow-sm rounded-md border px-3 py-2 mt-2 w-full", {"border-gray-400 focus:outline-blue-600": post.errors[:slug].none?, "border-red-400 focus:outline-red-600": post.errors[:slug].any?}] %>
  </div>

  <div class="my-5">
    <%= form.label :excerpt %>
    <%= form.textarea :excerpt, rows: 4, class: ["block shadow-sm rounded-md border px-3 py-2 mt-2 w-full", {"border-gray-400 focus:outline-blue-600": post.errors[:excerpt].none?, "border-red-400 focus:outline-red-600": post.errors[:excerpt].any?}] %>
  </div>

  <div class="my-5">
    <%= form.label :body %>
    <div
      data-controller="tiptap"
      data-tiptap-content-value="<%= @post.body.to_json %>"
    >
      <%= form.hidden_field :body, data: { "tiptap-target": "input" } %>
      <div data-tiptap-target="editor" class="border rounded-md" style="min-height: 250px;"></div>

      <div class="mt-2 flex gap-2">
        <button type="button" class="btn" data-action="click->tiptap#insertImageByUrl">Insert URL image</button>

        <label class="btn">
          Upload image
          <input type="file" accept="image/*" data-action="change->tiptap#uploadFile" data-tiptap-target="fileInput" style="display:none" />
        </label>        
        <button type="button" data-align="left" data-action="click->tiptap#setAlign">Left</button>
        <button type="button" data-align="center" data-action="click->tiptap#setAlign">Center</button>
        <button type="button" data-align="right" data-action="click->tiptap#setAlign">Right</button>
        <button type="button" data-align="justify" data-action="click->tiptap#setAlign">Justify</button>
      </div>
    </div>
  </div>

  <div class="my-5">
    <%= form.label :seo_title %>
    <%= form.text_field :seo_title, class: ["block shadow-sm rounded-md border px-3 py-2 mt-2 w-full", {"border-gray-400 focus:outline-blue-600": post.errors[:seo_title].none?, "border-red-400 focus:outline-red-600": post.errors[:seo_title].any?}] %>
  </div>

  <div class="my-5">
    <%= form.label :meta_description %>
    <%= form.text_field :meta_description, class: ["block shadow-sm rounded-md border px-3 py-2 mt-2 w-full", {"border-gray-400 focus:outline-blue-600": post.errors[:meta_description].none?, "border-red-400 focus:outline-red-600": post.errors[:meta_description].any?}] %>
  </div>

  <div class="my-5 flex items-center gap-2">
    <%= form.label :published %>
    <%= form.checkbox :published, class: ["block shadow-sm rounded-md border order-first h-5 w-5", {"border-gray-400 focus:outline-blue-600": post.errors[:published].none?, "border-red-400 focus:outline-red-600": post.errors[:published].any?}] %>
  </div>

  <div class="my-5">
    <%= form.label :published_at %>
    <%= form.datetime_field :published_at, class: ["block shadow-sm rounded-md border px-3 py-2 mt-2 w-full", {"border-gray-400 focus:outline-blue-600": post.errors[:published_at].none?, "border-red-400 focus:outline-red-600": post.errors[:published_at].any?}] %>
  </div>

  <div class="inline">
    <%= form.submit class: "w-full sm:w-auto rounded-md px-3.5 py-2.5 bg-blue-600 hover:bg-blue-500 text-white inline-block font-medium cursor-pointer" %>
  </div>
<% end %>
